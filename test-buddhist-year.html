<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบ Bootstrap DateTimePicker พ.ศ.</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css">
    
    <!-- Bootstrap DateTimePicker CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datetimepicker/4.17.47/css/bootstrap-datetimepicker.min.css">
    
    <style>
        .container {
            margin-top: 50px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ทดสอบ Bootstrap DateTimePicker แสดงปี พ.ศ.</h1>
        
        <div class="test-section">
            <h3>ทดสอบ Date Picker</h3>
            <div class="form-group">
                <label for="datepicker1">เลือกวันที่:</label>
                <div class="input-group date" id="datepicker1">
                    <input type="text" class="form-control" />
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>ทดสอบ DateTime Picker</h3>
            <div class="form-group">
                <label for="datetimepicker1">เลือกวันที่และเวลา:</label>
                <div class="input-group date" id="datetimepicker1">
                    <input type="text" class="form-control" />
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>ทดสอบ Year Picker</h3>
            <div class="form-group">
                <label for="yearpicker1">เลือกปี:</label>
                <div class="input-group date" id="yearpicker1">
                    <input type="text" class="form-control" />
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span>
                </div>
            </div>
        </div>
        
        <div class="alert alert-info">
            <h4>วิธีทดสอบ:</h4>
            <ul>
                <li>คลิกที่ input field หรือไอคอนปฏิทิน</li>
                <li>ทดสอบการแสดงปี พ.ศ. ในส่วนต่างๆ</li>
                <li>ทดสอบการเลือกปีและ decade</li>
                <li>ตรวจสอบว่าค่าที่เลือกถูกต้อง</li>
            </ul>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
    
    <!-- Moment.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/locale/th.min.js"></script>
    
    <!-- Bootstrap DateTimePicker JS (ไฟล์ที่แก้ไขแล้ว) -->
    <script src="bootstrap-datetimepicker-4.17.47.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // ตั้งค่า locale เป็นภาษาไทย
            moment.locale('th');
            
            // Date Picker
            $('#datepicker1').datetimepicker({
                format: 'DD/MM/YYYY',
                locale: 'th'
            });
            
            // DateTime Picker
            $('#datetimepicker1').datetimepicker({
                format: 'DD/MM/YYYY HH:mm',
                locale: 'th'
            });
            
            // Year Picker
            $('#yearpicker1').datetimepicker({
                format: 'YYYY',
                viewMode: 'years',
                locale: 'th'
            });
            
            // Event handlers สำหรับดูค่าที่เลือก
            $('#datepicker1').on('dp.change', function(e) {
                console.log('Date selected:', e.date ? e.date.format() : 'null');
            });
            
            $('#datetimepicker1').on('dp.change', function(e) {
                console.log('DateTime selected:', e.date ? e.date.format() : 'null');
            });
            
            $('#yearpicker1').on('dp.change', function(e) {
                console.log('Year selected:', e.date ? e.date.format() : 'null');
            });
        });
    </script>
</body>
</html>
