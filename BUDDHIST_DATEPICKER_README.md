# Buddhist Date Picker

Buddhist Date Picker เป็น component ที่รองรับการแสดงปี พ.ศ. (Buddhist Era) สำหรับการใช้งานในประเทศไทย

## ปัญหาที่แก้ไข

ปัญหาเดิมของ date picker ที่ใช้ moment.js กับ locale-th:
- ในโหมด month picker และ year picker ตรง title แสดงเป็น ค.ศ. แทนที่จะเป็น พ.ศ.
- ตัวเลือกปีใน picker body แสดงเป็น ค.ศ.
- การแสดงช่วงปี (เช่น 2000-2020) แสดงเป็น ค.ศ.

## การแก้ไข

### 1. ปรับปรุง locale-th.min.js

```javascript
// เพิ่มฟังก์ชันช่วยสำหรับการแปลงปี
function convertToBuddhistYear(year) {
    const yearNum = parseInt(year);
    if (yearNum >= 1900 && yearNum <= 2100) {
        return yearNum + 543;
    }
    return yearNum;
}

// ปรับปรุง postformat function
postformat: function (string) {
    return string
        // แปลงปีในรูปแบบช่วงปี เช่น 2020-2030
        .replace(/(\d{4})-(\d{4})/g, function (_, startYear, endYear) {
            return convertToBuddhistYear(startYear) + '-' + convertToBuddhistYear(endYear);
        })
        // แปลงปีในรูปแบบ Month YYYY
        .replace(/(มกราคม|กุมภาพันธ์|...)\s+(\d{4})/g, function (_, month, year) {
            return month + ' ' + convertToBuddhistYear(year);
        })
        // แปลงปีที่อยู่ในรูปแบบ YYYY
        .replace(/\b(\d{4})\b/g, function (_, year) {
            return convertToBuddhistYear(year);
        });
}
```

### 2. สร้าง Utility Functions

ไฟล์: `src/utils/buddhist-calendar.ts`

```typescript
// แปลงปี ค.ศ. เป็น พ.ศ.
export function convertToBuddhistYear(christianYear: number): number

// แปลงปี พ.ศ. เป็น ค.ศ.
export function convertToChristianYear(buddhistYear: number): number

// ฟอร์แมตวันที่เป็นรูปแบบไทย
export function formatDateThai(date: Date, format?: string): string
```

### 3. สร้าง Custom Hook

ไฟล์: `src/hooks/useBuddhistDatePicker.ts`

```typescript
export function useBuddhistDatePicker() {
    return {
        initializeBuddhistDatePicker,
        convertDatePickerElements,
        convertInputValue,
        convertDateToDisplay,
        parseDisplayDate,
    };
}
```

### 4. สร้าง Buddhist Date Picker Component

ไฟล์: `src/components/ui/buddhist-date-picker.tsx`

```typescript
export function BuddhistDatePicker({
    value,
    onChange,
    placeholder,
    disabled,
    className,
    format,
    showIcon,
}: BuddhistDatePickerProps)
```

## การใช้งาน

### Basic Usage

```tsx
import { BuddhistDatePicker } from '@/components/ui/buddhist-date-picker';

function MyComponent() {
    const [date, setDate] = useState<Date | null>(null);
    
    return (
        <BuddhistDatePicker
            value={date}
            onChange={setDate}
            placeholder="เลือกวันที่..."
        />
    );
}
```

### ใช้กับ moment.js ที่มีอยู่

```tsx
import { useBuddhistDatePicker } from '@/hooks/useBuddhistDatePicker';

function MyComponent() {
    const { initializeBuddhistDatePicker } = useBuddhistDatePicker();
    
    useEffect(() => {
        const cleanup = initializeBuddhistDatePicker();
        return cleanup;
    }, []);
    
    // ใช้ date picker library อื่นๆ ตามปกติ
    // ปีจะถูกแปลงเป็น พ.ศ. อัตโนมัติ
}
```

### Utility Functions

```tsx
import { 
    formatDateThai, 
    convertToBuddhistYear,
    convertToChristianYear 
} from '@/utils/buddhist-calendar';

// แปลงปี
const buddhistYear = convertToBuddhistYear(2024); // 2567
const christianYear = convertToChristianYear(2567); // 2024

// ฟอร์แมตวันที่
const thaiDate = formatDateThai(new Date()); // "25/09/2567"
const longFormat = formatDateThai(new Date(), 'D MMMM YYYY'); // "25 กันยายน 2567"
```

## คุณสมบัติ

- ✅ แสดงปีเป็น พ.ศ. ในทุกส่วนของ picker
- ✅ รองรับการพิมพ์วันที่โดยตรงในรูปแบบ DD/MM/YYYY (พ.ศ.)
- ✅ แปลงปีอัตโนมัติระหว่าง ค.ศ. และ พ.ศ.
- ✅ รองรับการเลือกเดือนและปีจาก dropdown
- ✅ แสดงชื่อเดือนเป็นภาษาไทย
- ✅ ใช้งานง่ายเหมือน date picker ทั่วไป
- ✅ รองรับการแสดงช่วงปี (เช่น 2563-2573)
- ✅ ทำงานร่วมกับ date picker libraries อื่นๆ ได้

## ทดสอบ

เข้าไปที่หน้า `/test-buddhist-datepicker` เพื่อทดสอบการทำงาน

## ไฟล์ที่เกี่ยวข้อง

- `locale-th.min.js` - moment.js locale สำหรับภาษาไทย (ปรับปรุงแล้ว)
- `src/utils/buddhist-calendar.ts` - utility functions
- `src/hooks/useBuddhistDatePicker.ts` - custom hook
- `src/components/ui/buddhist-date-picker.tsx` - main component
- `src/app/test-buddhist-datepicker/page.tsx` - หน้าทดสอบ
- `src/styles/globals.css` - CSS styles

## หมายเหตุ

- Component นี้จะแปลงปีอัตโนมัติระหว่าง ค.ศ. และ พ.ศ.
- ค่าที่ส่งกลับจาก onChange จะเป็น Date object ปกติ (ใช้ปี ค.ศ.)
- การแสดงผลเท่านั้นที่จะเป็น พ.ศ.
- รองรับปี ค.ศ. 1900-2100 (พ.ศ. 2443-2643)
