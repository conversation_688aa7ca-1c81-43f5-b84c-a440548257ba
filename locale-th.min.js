!function (e, _) {
    "object" == typeof exports && "undefined" != typeof module && "function" == typeof require
        ? _(require("moment"))
        : "function" == typeof define && define.amd
            ? define(["moment"], _)
            : _(e.moment);
}(this, function (moment) {
    "use strict";

    // -----------------------
    // ฟังก์ชันช่วยสำหรับการแปลงปี
    // -----------------------
    function convertToBuddhistYear(year) {
        const yearNum = parseInt(year);
        // ตรวจสอบว่าเป็นปี ค.ศ. หรือไม่ (ระหว่าง 1900-2100)
        if (yearNum >= 1900 && yearNum <= 2100) {
            return yearNum + 543;
        }
        return yearNum;
    }

    function convertToChristianYear(year) {
        const yearNum = parseInt(year);
        // ตรวจสอบว่าเป็นปี พ.ศ. หรือไม่ (ระหว่าง 2443-2643)
        if (yearNum >= 2443 && yearNum <= 2643) {
            return yearNum - 543;
        }
        return yearNum;
    }

    // -----------------------
    // กำหนด locale ภาษาไทย
    // -----------------------
    moment.defineLocale("th", {
        months: "มกราคม_กุมภาพันธ์_มีนาคม_เมษายน_พฤษภาคม_มิถุนายน_กรกฎาคม_สิงหาคม_กันยายน_ตุลาคม_พฤศจิกายน_ธันวาคม".split("_"),
        monthsShort: "ม.ค._ก.พ._มี.ค._เม.ย._พ.ค._มิ.ย._ก.ค._ส.ค._ก.ย._ต.ค._พ.ย._ธ.ค.".split("_"),
        monthsParseExact: true,
        weekdays: "อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัสบดี_ศุกร์_เสาร์".split("_"),
        weekdaysShort: "อาทิตย์_จันทร์_อังคาร_พุธ_พฤหัส_ศุกร์_เสาร์".split("_"),
        weekdaysMin: "อา._จ._อ._พ._พฤ._ศ._ส.".split("_"),
        weekdaysParseExact: true,
        longDateFormat: {
            LT: "H:mm",
            LTS: "H:mm:ss",
            L: "DD/MM/YYYY",
            LL: "D MMMM YYYY",
            LLL: "D MMMM YYYY เวลา H:mm",
            LLLL: "วันddddที่ D MMMM YYYY เวลา H:mm"
        },
        meridiemParse: /ก่อนเที่ยง|หลังเที่ยง/,
        isPM: h => h === "หลังเที่ยง",
        meridiem: h => (h < 12 ? "ก่อนเที่ยง" : "หลังเที่ยง"),
        calendar: {
            sameDay: "[วันนี้ เวลา] LT",
            nextDay: "[พรุ่งนี้ เวลา] LT",
            nextWeek: "dddd[หน้า เวลา] LT",
            lastDay: "[เมื่อวานนี้ เวลา] LT",
            lastWeek: "[วัน]dddd[ที่แล้ว เวลา] LT",
            sameElse: "L"
        },
        relativeTime: {
            future: "อีก %s",
            past: "%sที่แล้ว",
            s: "ไม่กี่วินาที",
            ss: "%d วินาที",
            m: "1 นาที",
            mm: "%d นาที",
            h: "1 ชั่วโมง",
            hh: "%d ชั่วโมง",
            d: "1 วัน",
            dd: "%d วัน",
            w: "1 สัปดาห์",
            ww: "%d สัปดาห์",
            M: "1 เดือน",
            MM: "%d เดือน",
            y: "1 ปี",
            yy: "%d ปี"
        },

        // แปลง พ.ศ. เป็น ค.ศ. เมื่อ parse
        preparse: function (string) {
            return string.replace(/\b(\d{4})\b/g, function (_, year) {
                return convertToChristianYear(year);
            });
        },

        // แปลง ค.ศ. เป็น พ.ศ. เมื่อแสดงผล
        postformat: function (string) {
            // แปลงปีในรูปแบบต่างๆ
            return string
                // แปลงปีในรูปแบบช่วงปี เช่น 2020-2030 (ต้องทำก่อนการแปลงปีเดี่ยว)
                .replace(/(\d{4})-(\d{4})/g, function (_, startYear, endYear) {
                    return convertToBuddhistYear(startYear) + '-' + convertToBuddhistYear(endYear);
                })
                // แปลงปีที่อยู่หลัง "ปี " เช่น "ปี 2024"
                .replace(/(ปี\s*)(\d{4})/g, function (_, prefix, year) {
                    return prefix + convertToBuddhistYear(year);
                })
                // แปลงปีในรูปแบบ Month YYYY เช่น "มกราคม 2024"
                .replace(/(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)\s+(\d{4})/g, function (_, month, year) {
                    return month + ' ' + convertToBuddhistYear(year);
                })
                // แปลงปีในรูปแบบ MM/YYYY เช่น "01/2024"
                .replace(/(\d{1,2})\/(\d{4})/g, function (_, month, year) {
                    return month + '/' + convertToBuddhistYear(year);
                })
                // แปลงปีที่อยู่ในรูปแบบ YYYY (4 หลัก) - ทำเป็นอันสุดท้าย
                .replace(/\b(\d{4})\b/g, function (_, year) {
                    return convertToBuddhistYear(year);
                });
        }

    });

});
