'use client';

import React, { useState, useEffect } from 'react';
import { BuddhistDatePicker } from '@/components/ui/buddhist-date-picker';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { formatDateThai, convertToBuddhistYear } from '@/utils/buddhist-calendar';
import { useBuddhistDatePicker } from '@/hooks/useBuddhistDatePicker';

export default function TestBuddhistDatePickerPage() {
  const router = useRouter();
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedDate2, setSelectedDate2] = useState<Date | null>(new Date());
  
  const {
    initializeBuddhistDatePicker,
    convertDateToDisplay,
    convertInputValue,
  } = useBuddhistDatePicker();

  // Initialize Buddhist date picker functionality
  useEffect(() => {
    const cleanup = initializeBuddhistDatePicker();
    return cleanup;
  }, [initializeBuddhistDatePicker]);

  const handleGoBack = () => {
    router.back();
  };

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
    console.log('Selected date:', date);
    if (date) {
      console.log('Buddhist year format:', formatDateThai(date));
      console.log('Display format:', convertDateToDisplay(date));
    }
  };

  const handleDateChange2 = (date: Date | null) => {
    setSelectedDate2(date);
  };

  const getCurrentBuddhistYear = () => {
    return convertToBuddhistYear(new Date().getFullYear());
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      {/* Header */}
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleGoBack}
          className="mr-4"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h1 className="text-2xl font-bold text-gray-900">
          ทดสอบ Buddhist Date Picker
        </h1>
      </div>

      <div className="max-w-4xl mx-auto space-y-6">
        {/* Basic Usage */}
        <Card>
          <CardHeader>
            <CardTitle>การใช้งานพื้นฐาน</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                เลือกวันที่ (แสดงปี พ.ศ.)
              </label>
              <BuddhistDatePicker
                value={selectedDate}
                onChange={handleDateChange}
                placeholder="เลือกวันที่..."
                className="w-full max-w-xs"
              />
            </div>
            
            {selectedDate && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">ข้อมูลวันที่ที่เลือก:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li><strong>Date Object:</strong> {selectedDate.toString()}</li>
                  <li><strong>รูปแบบไทย:</strong> {formatDateThai(selectedDate)}</li>
                  <li><strong>รูปแบบแสดงผล:</strong> {convertDateToDisplay(selectedDate)}</li>
                  <li><strong>ปี ค.ศ.:</strong> {selectedDate.getFullYear()}</li>
                  <li><strong>ปี พ.ศ.:</strong> {convertToBuddhistYear(selectedDate.getFullYear())}</li>
                </ul>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pre-selected Date */}
        <Card>
          <CardHeader>
            <CardTitle>วันที่ที่กำหนดไว้ล่วงหน้า</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                วันที่ปัจจุบัน (กำหนดไว้ล่วงหน้า)
              </label>
              <BuddhistDatePicker
                value={selectedDate2}
                onChange={handleDateChange2}
                placeholder="เลือกวันที่..."
                className="w-full max-w-xs"
              />
            </div>
            
            {selectedDate2 && (
              <div className="p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">ข้อมูลวันที่:</h4>
                <ul className="text-sm text-green-800 space-y-1">
                  <li><strong>รูปแบบไทย:</strong> {formatDateThai(selectedDate2)}</li>
                  <li><strong>ปี พ.ศ.:</strong> {convertToBuddhistYear(selectedDate2.getFullYear())}</li>
                </ul>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Information */}
        <Card>
          <CardHeader>
            <CardTitle>ข้อมูลเพิ่มเติม</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">คุณสมบัติของ Buddhist Date Picker:</h4>
                <ul className="text-sm text-yellow-800 space-y-1 list-disc list-inside">
                  <li>แสดงปีเป็น พ.ศ. ในทุกส่วนของ picker</li>
                  <li>รองรับการพิมพ์วันที่โดยตรงในรูปแบบ DD/MM/YYYY (พ.ศ.)</li>
                  <li>แปลงปีอัตโนมัติระหว่าง ค.ศ. และ พ.ศ.</li>
                  <li>รองรับการเลือกเดือนและปีจาก dropdown</li>
                  <li>แสดงชื่อเดือนเป็นภาษาไทย</li>
                  <li>ใช้งานง่ายเหมือน date picker ทั่วไป</li>
                </ul>
              </div>
              
              <div className="p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-2">ข้อมูลปัจจุบัน:</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li><strong>ปีปัจจุบัน (ค.ศ.):</strong> {new Date().getFullYear()}</li>
                  <li><strong>ปีปัจจุบัน (พ.ศ.):</strong> {getCurrentBuddhistYear()}</li>
                  <li><strong>วันที่ปัจจุบัน:</strong> {formatDateThai(new Date())}</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Usage Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>วิธีการใช้งาน</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <h4>การใช้งานใน Component อื่น:</h4>
              <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-x-auto">
{`import { BuddhistDatePicker } from '@/components/ui/buddhist-date-picker';

function MyComponent() {
  const [date, setDate] = useState<Date | null>(null);
  
  return (
    <BuddhistDatePicker
      value={date}
      onChange={setDate}
      placeholder="เลือกวันที่..."
    />
  );
}`}
              </pre>
              
              <h4 className="mt-4">การใช้งาน Utility Functions:</h4>
              <pre className="bg-gray-100 p-4 rounded-lg text-xs overflow-x-auto">
{`import { 
  formatDateThai, 
  convertToBuddhistYear,
  convertToChristianYear 
} from '@/utils/buddhist-calendar';

// แปลงปี ค.ศ. เป็น พ.ศ.
const buddhistYear = convertToBuddhistYear(2024); // 2567

// แปลงปี พ.ศ. เป็น ค.ศ.
const christianYear = convertToChristianYear(2567); // 2024

// ฟอร์แมตวันที่เป็นรูปแบบไทย
const thaiDate = formatDateThai(new Date()); // "25/09/2567"`}
              </pre>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
