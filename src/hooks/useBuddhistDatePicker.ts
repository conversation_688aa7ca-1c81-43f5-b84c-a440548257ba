import { useCallback, useEffect } from 'react';
import { convertToBuddhistYear, convertToChristianYear, convertTextToBuddhistYear } from '@/utils/buddhist-calendar';

/**
 * Custom hook สำหรับจัดการ date picker ที่รองรับ Buddhist calendar
 */
export function useBuddhistDatePicker() {
  
  /**
   * แปลง DOM elements ที่แสดงปีให้เป็น พ.ศ.
   */
  const convertDatePickerElements = useCallback(() => {
    // หา date picker elements ที่อาจจะมีปี ค.ศ.
    const selectors = [
      '.ant-picker-header-view', // Antd DatePicker header
      '.ant-picker-year-btn',     // Antd year button
      '.ant-picker-month-btn',    // Antd month button
      '.ant-picker-cell',         // Antd picker cells
      '.react-datepicker__year',  // React DatePicker year
      '.react-datepicker__month-year-text', // React DatePicker month-year
      '[class*="year"]',          // Generic year elements
      '[class*="picker"]',        // Generic picker elements
      '[data-testid*="year"]',    // Elements with year in test id
    ];

    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        if (element.textContent) {
          const originalText = element.textContent;
          const convertedText = convertTextToBuddhistYear(originalText);
          if (originalText !== convertedText) {
            element.textContent = convertedText;
          }
        }
      });
    });
  }, []);

  /**
   * สร้าง MutationObserver เพื่อติดตาม DOM changes
   */
  const setupMutationObserver = useCallback(() => {
    const observer = new MutationObserver((mutations) => {
      let shouldConvert = false;
      
      mutations.forEach((mutation) => {
        // ตรวจสอบว่ามี node ใหม่ที่เพิ่มเข้ามา
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              const element = node as Element;
              // ตรวจสอบว่าเป็น date picker element หรือไม่
              if (element.classList.contains('ant-picker-dropdown') ||
                  element.classList.contains('react-datepicker') ||
                  element.querySelector('[class*="picker"]') ||
                  element.querySelector('[class*="calendar"]')) {
                shouldConvert = true;
              }
            }
          });
        }
        
        // ตรวจสอบการเปลี่ยนแปลง text content
        if (mutation.type === 'characterData' || 
            (mutation.type === 'childList' && mutation.target.textContent)) {
          const target = mutation.target as Element;
          if (target.closest('[class*="picker"]') || 
              target.closest('[class*="calendar"]')) {
            shouldConvert = true;
          }
        }
      });

      if (shouldConvert) {
        // ใช้ setTimeout เพื่อให้ DOM render เสร็จก่อน
        setTimeout(convertDatePickerElements, 10);
      }
    });

    // เริ่มติดตาม DOM changes
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    return observer;
  }, [convertDatePickerElements]);

  /**
   * แปลงปีใน input value จาก พ.ศ. เป็น ค.ศ. สำหรับการ submit
   */
  const convertInputValue = useCallback((value: string): string => {
    // แปลงรูปแบบวันที่ต่างๆ
    return value
      // รูปแบบ DD/MM/YYYY (พ.ศ.) -> DD/MM/YYYY (ค.ศ.)
      .replace(/(\d{1,2})\/(\d{1,2})\/(\d{4})/g, (_, day, month, year) => {
        const christianYear = convertToChristianYear(parseInt(year));
        return `${day}/${month}/${christianYear}`;
      })
      // รูปแบบ YYYY-MM-DD (พ.ศ.) -> YYYY-MM-DD (ค.ศ.)
      .replace(/(\d{4})-(\d{1,2})-(\d{1,2})/g, (_, year, month, day) => {
        const christianYear = convertToChristianYear(parseInt(year));
        return `${christianYear}-${month}-${day}`;
      })
      // รูปแบบปีเดี่ยว
      .replace(/\b(\d{4})\b/g, (_, year) => {
        return convertToChristianYear(parseInt(year)).toString();
      });
  }, []);

  /**
   * แปลงปีใน Date object เป็น พ.ศ. สำหรับการแสดงผล
   */
  const convertDateToDisplay = useCallback((date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const buddhistYear = convertToBuddhistYear(date.getFullYear());
    return `${day}/${month}/${buddhistYear}`;
  }, []);

  /**
   * สร้าง Date object จากค่าที่เป็น พ.ศ.
   */
  const parseDisplayDate = useCallback((dateString: string): Date | null => {
    // รูปแบบ DD/MM/YYYY (พ.ศ.)
    const ddmmyyyyMatch = dateString.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})/);
    if (ddmmyyyyMatch) {
      const [, day, month, year] = ddmmyyyyMatch;
      const christianYear = convertToChristianYear(parseInt(year));
      return new Date(christianYear, parseInt(month) - 1, parseInt(day));
    }

    // รูปแบบ YYYY-MM-DD (พ.ศ.)
    const yyyymmddMatch = dateString.match(/(\d{4})-(\d{1,2})-(\d{1,2})/);
    if (yyyymmddMatch) {
      const [, year, month, day] = yyyymmddMatch;
      const christianYear = convertToChristianYear(parseInt(year));
      return new Date(christianYear, parseInt(month) - 1, parseInt(day));
    }

    return null;
  }, []);

  /**
   * Initialize the Buddhist date picker functionality
   */
  const initializeBuddhistDatePicker = useCallback(() => {
    // แปลง elements ที่มีอยู่แล้ว
    convertDatePickerElements();
    
    // ตั้งค่า MutationObserver
    const observer = setupMutationObserver();
    
    // Cleanup function
    return () => {
      observer.disconnect();
    };
  }, [convertDatePickerElements, setupMutationObserver]);

  return {
    initializeBuddhistDatePicker,
    convertDatePickerElements,
    convertInputValue,
    convertDateToDisplay,
    parseDisplayDate,
    convertToBuddhistYear,
    convertToChristianYear,
  };
}
