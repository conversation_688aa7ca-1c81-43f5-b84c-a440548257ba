"use client"

import * as React from "react"
import { useEffect, useRef } from "react"
import { Calendar, CalendarDays } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useBuddhistDatePicker } from "@/hooks/useBuddhistDatePicker"
import { formatDateThai } from "@/utils/buddhist-calendar"

interface BuddhistDatePickerProps {
  value?: Date | null
  onChange?: (date: Date | null) => void
  placeholder?: string
  disabled?: boolean
  className?: string
  format?: string
  showIcon?: boolean
}

/**
 * Buddhist Date Picker Component
 * รองรับการแสดงปี พ.ศ. และการแปลงปีอัตโนมัติ
 */
export function BuddhistDatePicker({
  value,
  onChange,
  placeholder = "เลือกวันที่",
  disabled = false,
  className,
  format = "DD/MM/YYYY",
  showIcon = true,
}: BuddhistDatePickerProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const [isOpen, setIsOpen] = React.useState(false)
  const [inputValue, setInputValue] = React.useState("")
  
  const {
    initializeBuddhistDatePicker,
    convertDateToDisplay,
    parseDisplayDate,
    convertToBuddhistYear,
  } = useBuddhistDatePicker()

  // Initialize Buddhist date picker functionality
  useEffect(() => {
    const cleanup = initializeBuddhistDatePicker()
    return cleanup
  }, [initializeBuddhistDatePicker])

  // Update input value when value prop changes
  useEffect(() => {
    if (value) {
      const displayValue = convertDateToDisplay(value)
      setInputValue(displayValue)
    } else {
      setInputValue("")
    }
  }, [value, convertDateToDisplay])

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    
    // Try to parse the date
    const parsedDate = parseDisplayDate(newValue)
    if (parsedDate && onChange) {
      onChange(parsedDate)
    } else if (newValue === "" && onChange) {
      onChange(null)
    }
  }

  // Handle input blur
  const handleInputBlur = () => {
    if (inputValue && value) {
      // Reformat the input value to ensure consistency
      const displayValue = convertDateToDisplay(value)
      setInputValue(displayValue)
    }
  }

  // Generate years for dropdown (Buddhist years)
  const generateYears = () => {
    const currentYear = new Date().getFullYear()
    const currentBuddhistYear = convertToBuddhistYear(currentYear)
    const years = []
    
    // Generate 10 years before and after current year
    for (let i = currentBuddhistYear - 10; i <= currentBuddhistYear + 10; i++) {
      years.push(i)
    }
    
    return years
  }

  // Generate months
  const generateMonths = () => {
    return [
      'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
      'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
    ]
  }

  // Handle date selection from calendar
  const handleDateSelect = (selectedDate: Date) => {
    if (onChange) {
      onChange(selectedDate)
    }
    setIsOpen(false)
  }

  // Simple calendar component
  const Calendar = () => {
    const currentDate = value || new Date()
    const currentMonth = currentDate.getMonth()
    const currentYear = currentDate.getFullYear()
    const buddhistYear = convertToBuddhistYear(currentYear)
    
    const [displayMonth, setDisplayMonth] = React.useState(currentMonth)
    const [displayYear, setDisplayYear] = React.useState(buddhistYear)
    
    const months = generateMonths()
    const years = generateYears()
    
    // Get days in month
    const getDaysInMonth = (month: number, year: number) => {
      return new Date(year, month + 1, 0).getDate()
    }
    
    // Get first day of month (0 = Sunday, 1 = Monday, etc.)
    const getFirstDayOfMonth = (month: number, year: number) => {
      return new Date(year, month, 1).getDay()
    }
    
    const daysInMonth = getDaysInMonth(displayMonth, displayYear - 543)
    const firstDay = getFirstDayOfMonth(displayMonth, displayYear - 543)
    
    const days = []
    
    // Empty cells for days before the first day of month
    for (let i = 0; i < firstDay; i++) {
      days.push(<div key={`empty-${i}`} className="p-2"></div>)
    }
    
    // Days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const isSelected = value && 
        value.getDate() === day && 
        value.getMonth() === displayMonth && 
        value.getFullYear() === (displayYear - 543)
      
      days.push(
        <button
          key={day}
          onClick={() => handleDateSelect(new Date(displayYear - 543, displayMonth, day))}
          className={cn(
            "calendar-day p-2 text-sm rounded",
            isSelected && "selected"
          )}
        >
          {day}
        </button>
      )
    }
    
    return (
      <div className="p-4 bg-white border rounded-lg shadow-lg">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <select
            value={displayMonth}
            onChange={(e) => setDisplayMonth(parseInt(e.target.value))}
            className="px-2 py-1 border rounded"
          >
            {months.map((month, index) => (
              <option key={index} value={index}>{month}</option>
            ))}
          </select>
          
          <select
            value={displayYear}
            onChange={(e) => setDisplayYear(parseInt(e.target.value))}
            className="px-2 py-1 border rounded"
          >
            {years.map((year) => (
              <option key={year} value={year}>{year}</option>
            ))}
          </select>
        </div>
        
        {/* Days of week */}
        <div className="grid grid-cols-7 gap-1 mb-2">
          {['อา', 'จ', 'อ', 'พ', 'พฤ', 'ศ', 'ส'].map((day) => (
            <div key={day} className="p-2 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}
        </div>
        
        {/* Calendar days */}
        <div className="grid grid-cols-7 gap-1">
          {days}
        </div>
      </div>
    )
  }

  return (
    <div ref={containerRef} className={cn("relative buddhist-datepicker", className)}>
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          disabled={disabled}
          className="pr-10"
        />

        {showIcon && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setIsOpen(!isOpen)}
            disabled={disabled}
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          >
            <CalendarDays className="h-4 w-4 text-gray-500" />
          </Button>
        )}
      </div>

      {isOpen && !disabled && (
        <div className="absolute top-full left-0 z-50 mt-1">
          <div className="calendar-dropdown">
            <Calendar />
          </div>
        </div>
      )}

      {/* Overlay to close calendar when clicking outside */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  )
}
