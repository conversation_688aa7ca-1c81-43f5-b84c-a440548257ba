/**
 * Utility functions for Buddhist calendar conversion
 * สำหรับการแปลงปีระหว่าง ค.ศ. และ พ.ศ.
 */

/**
 * แปลงปี ค.ศ. เป็น พ.ศ.
 * @param christianYear ปี ค.ศ.
 * @returns ปี พ.ศ.
 */
export function convertToBuddhistYear(christianYear: number): number {
  // ตรวจสอบว่าเป็นปี ค.ศ. หรือไม่ (ระหว่าง 1900-2100)
  if (christianYear >= 1900 && christianYear <= 2100) {
    return christianYear + 543;
  }
  return christianYear;
}

/**
 * แปลงปี พ.ศ. เป็น ค.ศ.
 * @param buddhistYear ปี พ.ศ.
 * @returns ปี ค.ศ.
 */
export function convertToChristianYear(buddhistYear: number): number {
  // ตรวจสอบว่าเป็นปี พ.ศ. หรือไม่ (ระหว่าง 2443-2643)
  if (buddhistYear >= 2443 && buddhistYear <= 2643) {
    return buddhistYear - 543;
  }
  return buddhistYear;
}

/**
 * แปลงข้อความที่มีปีจาก ค.ศ. เป็น พ.ศ.
 * @param text ข้อความที่มีปี
 * @returns ข้อความที่แปลงปีเป็น พ.ศ. แล้ว
 */
export function convertTextToBuddhistYear(text: string): string {
  return text
    // แปลงปีที่อยู่ในรูปแบบ YYYY (4 หลัก)
    .replace(/\b(\d{4})\b/g, (_, year) => {
      return convertToBuddhistYear(parseInt(year)).toString();
    })
    // แปลงปีในรูปแบบช่วงปี เช่น 2020-2030
    .replace(/(\d{4})-(\d{4})/g, (_, startYear, endYear) => {
      return `${convertToBuddhistYear(parseInt(startYear))}-${convertToBuddhistYear(parseInt(endYear))}`;
    })
    // แปลงปีที่อยู่หลัง "ปี " เช่น "ปี 2024"
    .replace(/(ปี\s*)(\d{4})/g, (_, prefix, year) => {
      return prefix + convertToBuddhistYear(parseInt(year));
    });
}

/**
 * แปลงข้อความที่มีปีจาก พ.ศ. เป็น ค.ศ.
 * @param text ข้อความที่มีปี
 * @returns ข้อความที่แปลงปีเป็น ค.ศ. แล้ว
 */
export function convertTextToChristianYear(text: string): string {
  return text
    // แปลงปีที่อยู่ในรูปแบบ YYYY (4 หลัก)
    .replace(/\b(\d{4})\b/g, (_, year) => {
      return convertToChristianYear(parseInt(year)).toString();
    })
    // แปลงปีในรูปแบบช่วงปี เช่น 2563-2573
    .replace(/(\d{4})-(\d{4})/g, (_, startYear, endYear) => {
      return `${convertToChristianYear(parseInt(startYear))}-${convertToChristianYear(parseInt(endYear))}`;
    })
    // แปลงปีที่อยู่หลัง "ปี " เช่น "ปี 2567"
    .replace(/(ปี\s*)(\d{4})/g, (_, prefix, year) => {
      return prefix + convertToChristianYear(parseInt(year));
    });
}

/**
 * ฟอร์แมตวันที่เป็นรูปแบบไทย (พ.ศ.)
 * @param date วันที่
 * @param format รูปแบบการแสดงผล (default: 'DD/MM/YYYY')
 * @returns วันที่ในรูปแบบไทย
 */
export function formatDateThai(date: Date, format: string = 'DD/MM/YYYY'): string {
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = convertToBuddhistYear(date.getFullYear());
  
  switch (format) {
    case 'DD/MM/YYYY':
      return `${day}/${month}/${year}`;
    case 'D MMMM YYYY':
      const monthNames = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ];
      return `${parseInt(day)} ${monthNames[date.getMonth()]} ${year}`;
    default:
      return `${day}/${month}/${year}`;
  }
}

/**
 * สร้าง Date object จากปี พ.ศ.
 * @param buddhistYear ปี พ.ศ.
 * @param month เดือน (1-12)
 * @param day วัน
 * @returns Date object
 */
export function createDateFromBuddhistYear(buddhistYear: number, month: number, day: number): Date {
  const christianYear = convertToChristianYear(buddhistYear);
  return new Date(christianYear, month - 1, day);
}

/**
 * ตรวจสอบว่าปีเป็น พ.ศ. หรือไม่
 * @param year ปี
 * @returns true ถ้าเป็น พ.ศ.
 */
export function isBuddhistYear(year: number): boolean {
  return year >= 2443 && year <= 2643;
}

/**
 * ตรวจสอบว่าปีเป็น ค.ศ. หรือไม่
 * @param year ปี
 * @returns true ถ้าเป็น ค.ศ.
 */
export function isChristianYear(year: number): boolean {
  return year >= 1900 && year <= 2100;
}
